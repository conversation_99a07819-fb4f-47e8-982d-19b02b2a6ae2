<template>
  <q-page padding>
    <div class="q-pa-md">
      <h4>WebSocket 連接測試</h4>

      <div class="q-mb-md">
        <q-card>
          <q-card-section>
            <div class="text-h6">連接狀態</div>
            <div :class="connectionStatusClass">{{ connectionStatus }}</div>
          </q-card-section>
        </q-card>
      </div>

      <div class="q-mb-md">
        <q-card>
          <q-card-section>
            <div class="text-h6">調試信息</div>
            <div class="q-mt-sm">
              <div><strong>WebSocket URL:</strong> {{ wsUrl }}</div>
              <div><strong>Token:</strong> {{ token ? '已設置' : '未設置' }}</div>
              <div><strong>用戶ID:</strong> {{ authStore.user?.id || '未登入' }}</div>
            </div>
          </q-card-section>
        </q-card>
      </div>

      <div class="q-mb-md">
        <q-btn
          color="primary"
          label="重新連接WebSocket"
          @click="reconnectWebSocket"
          :disable="!authStore.isAuthenticated"
        />
        <q-btn
          color="secondary"
          label="斷開WebSocket"
          @click="disconnectWebSocket"
          class="q-ml-sm"
        />
        <q-btn
          color="orange"
          label="測試後端連接"
          @click="testBackendConnection"
          class="q-ml-sm"
        />
      </div>

      <div class="q-mb-md">
        <q-card>
          <q-card-section>
            <div class="text-h6">消息日誌</div>
            <div class="q-mt-sm" style="max-height: 300px; overflow-y: auto;">
              <div v-for="(message, index) in messages" :key="index" class="q-mb-xs">
                <span class="text-caption text-grey">{{ message.timestamp }}</span>
                <span :class="message.type === 'error' ? 'text-red' : 'text-green'">
                  {{ message.content }}
                </span>
              </div>
            </div>
          </q-card-section>
        </q-card>
      </div>
    </div>
  </q-page>
</template>

<script setup lang="ts">
import { ref, computed, onMounted, onUnmounted } from 'vue';
import { useAuthStore } from '@/stores/auth';

const authStore = useAuthStore();

const connectionStatus = ref('未連接');
const messages = ref<Array<{timestamp: string, content: string, type: string}>>([]);
let testWs: WebSocket | null = null;

const wsUrl = computed(() => {
  let url = import.meta.env.VITE_WS_URL;

  // 如果沒有設置環境變數，根據當前環境自動判斷
  if (!url) {
    const protocol = window.location.protocol === 'https:' ? 'wss:' : 'ws:';
    const host = window.location.hostname;
    // WebSocket直接連接到後端服務器
    const port = '8088';
    url = `${protocol}//${host}:${port}/api/ws`;
  }

  return url;
});

const token = computed(() => {
  return authStore.accessToken;
});

const connectionStatusClass = computed(() => {
  switch (connectionStatus.value) {
    case '已連接':
      return 'text-green text-weight-bold';
    case '連接中...':
      return 'text-orange text-weight-bold';
    case '連接失敗':
    case '連接關閉':
      return 'text-red text-weight-bold';
    default:
      return 'text-grey text-weight-bold';
  }
});

const addMessage = (content: string, type = 'info') => {
  const timestamp = new Date().toLocaleTimeString();
  messages.value.push({ timestamp, content, type });

  // 保持最多50條消息
  if (messages.value.length > 50) {
    messages.value.shift();
  }
};

const connectWebSocket = () => {
  if (!token.value) {
    addMessage('無法連接：未找到有效的token', 'error');
    return;
  }

  if (testWs) {
    addMessage('關閉現有WebSocket連接...');
    testWs.close();
    testWs = null;
  }

  connectionStatus.value = '連接中...';
  addMessage(`🔗 嘗試連接到: ${wsUrl.value}`);
  addMessage(`🔑 使用Token: ${token.value.substring(0, 50)}...`);
  addMessage(`🌐 當前域名: ${window.location.hostname}`);
  addMessage(`📍 當前協議: ${window.location.protocol}`);

  // 檢查網絡連接
  if (!navigator.onLine) {
    addMessage('❌ 網絡連接不可用', 'error');
    connectionStatus.value = '網絡錯誤';
    return;
  }

  try {
    testWs = new WebSocket(`${wsUrl.value}?token=${token.value}`);
    addMessage('📡 WebSocket對象已創建，等待連接...');

    // 設置連接超時
    const connectionTimeout = setTimeout(() => {
      if (testWs && testWs.readyState === WebSocket.CONNECTING) {
        addMessage('⏰ 連接超時，關閉連接', 'error');
        testWs.close();
        connectionStatus.value = '連接超時';
      }
    }, 10000); // 10秒超時

    testWs.onopen = () => {
      clearTimeout(connectionTimeout);
      connectionStatus.value = '已連接';
      addMessage('✅ WebSocket連接成功建立');
      addMessage(`📊 連接狀態: ${testWs?.readyState} (OPEN=1)`);
    };

    testWs.onmessage = (event) => {
      try {
        const data = JSON.parse(event.data);
        addMessage(`📨 收到JSON消息: ${JSON.stringify(data)}`);

        if (data.type === 'forced_logout') {
          addMessage('🚨 收到強制登出通知！', 'error');
        }
      } catch (error) {
        addMessage(`📨 收到原始消息: ${event.data}`);
      }
    };

    testWs.onclose = (event) => {
      clearTimeout(connectionTimeout);
      connectionStatus.value = '連接關閉';
      addMessage(`❌ WebSocket連接關閉: code=${event.code}, reason=${event.reason || '無原因'}`, 'error');

      // 詳細的錯誤代碼說明
      const errorMessages: Record<number, string> = {
        1000: '正常關閉',
        1001: '端點離開（如頁面刷新）',
        1002: '協議錯誤',
        1003: '不支持的數據類型',
        1004: '保留',
        1005: '未收到狀態碼',
        1006: '連接異常關閉（可能是網絡問題或服務器拒絕）',
        1007: '數據格式錯誤',
        1008: '策略違規',
        1009: '消息過大',
        1010: '缺少擴展',
        1011: '內部服務器錯誤',
        1015: 'TLS握手失敗'
      };

      const errorMsg = errorMessages[event.code] || '未知錯誤';
      addMessage(`📋 錯誤說明: ${errorMsg}`, 'error');

      if (event.code === 1006) {
        addMessage('💡 建議: 檢查服務器是否運行，網絡是否正常', 'error');
      }
    };

    testWs.onerror = (error) => {
      clearTimeout(connectionTimeout);
      connectionStatus.value = '連接失敗';
      addMessage(`❌ WebSocket連接錯誤: ${error}`, 'error');
      addMessage(`📊 連接狀態: ${testWs?.readyState} (CONNECTING=0, OPEN=1, CLOSING=2, CLOSED=3)`, 'error');
    };

  } catch (error) {
    connectionStatus.value = '創建失敗';
    addMessage(`❌ 創建WebSocket失敗: ${error}`, 'error');
  }
};

const reconnectWebSocket = () => {
  addMessage('手動重新連接WebSocket...');
  connectWebSocket();
};

const disconnectWebSocket = () => {
  if (testWs) {
    testWs.close(1000, 'Manual disconnect');
    testWs = null;
    connectionStatus.value = '未連接';
    addMessage('手動斷開WebSocket連接');
  }
};

const testBackendConnection = async () => {
  addMessage('🔍 開始測試後端連接...');

  try {
    // 測試HTTP連接
    const response = await fetch('/api/profile', {
      headers: {
        'Authorization': `Bearer ${token.value}`
      }
    });

    if (response.ok) {
      addMessage('✅ HTTP API連接正常');
    } else {
      addMessage(`❌ HTTP API連接失敗: ${response.status} ${response.statusText}`, 'error');
    }
  } catch (error) {
    addMessage(`❌ HTTP API連接錯誤: ${error}`, 'error');
  }

  // 測試WebSocket端點是否可達
  try {
    const wsTestUrl = wsUrl.value.replace('ws://', 'http://').replace('wss://', 'https://').replace('/api/ws', '/api/profile');
    addMessage(`🔗 測試WebSocket服務器: ${wsTestUrl}`);

    const response = await fetch(wsTestUrl, {
      headers: {
        'Authorization': `Bearer ${token.value}`
      }
    });

    if (response.ok) {
      addMessage('✅ WebSocket服務器可達');
    } else {
      addMessage(`❌ WebSocket服務器不可達: ${response.status}`, 'error');
    }
  } catch (error) {
    addMessage(`❌ WebSocket服務器測試失敗: ${error}`, 'error');
  }
};

onMounted(() => {
  addMessage('WebSocket測試頁面已載入');
  if (authStore.isAuthenticated) {
    connectWebSocket();
  } else {
    addMessage('用戶未登入，無法建立WebSocket連接', 'error');
  }
});

onUnmounted(() => {
  if (testWs) {
    testWs.close();
  }
});
</script>

<style scoped>
.q-card {
  margin-bottom: 16px;
}
</style>
