{"version": 3, "sources": ["../../../../quasar/lang/zh-tw.js", "dep:quasar_lang_zh-tw_js"], "sourcesContent": ["export default {\n  isoName: 'zh-TW',\n  nativeName: '中文（繁體）',\n  label: {\n    clear: '清除',\n    ok: '確定',\n    cancel: '取消',\n    close: '關閉',\n    set: '設定',\n    select: '選擇',\n    reset: '重置',\n    remove: '移除',\n    update: '更新',\n    create: '新增',\n    search: '搜尋',\n    filter: '篩選',\n    refresh: '更新',\n    expand: label => (label ? `展開\"${ label }\"` : '擴張'),\n    collapse: label => (label ? `折疊\"${ label }\"` : '坍塌')\n  },\n  date: {\n    days: '星期日_星期一_星期二_星期三_星期四_星期五_星期六'.split('_'),\n    daysShort: '週日_週一_週二_週三_週四_週五_週六'.split('_'),\n    months: '一月_二月_三月_四月_五月_六月_七月_八月_九月_十月_十一月_十二月'.split('_'),\n    monthsShort: '一月_二月_三月_四月_五月_六月_七月_八月_九月_十月_十一月_十二月'.split('_'),\n    headerTitle: date => new Intl.DateTimeFormat('zh-TW', {\n      weekday: 'short', month: 'short', day: 'numeric'\n    }).format(date),\n    firstDayOfWeek: 0, // 0-6, 0 - Sunday, 1 Monday, ...\n    format24h: false,\n    pluralDay: '日'\n  },\n  table: {\n    noData: '沒有資料',\n    noResults: '沒有相符資料',\n    loading: '載入中...',\n    selectedRecords: rows => '已選擇 ' + rows + ' 列',\n    recordsPerPage: '每頁列數：',\n    allRows: '全部',\n    pagination: (start, end, total) => start + '-' + end + ' 列，共 ' + total + ' 列',\n    columns: '欄位'\n  },\n  editor: {\n    url: '網址',\n    bold: '粗體',\n    italic: '斜體',\n    strikethrough: '刪除線',\n    underline: '下劃線',\n    unorderedList: '項目符號清單',\n    orderedList: '編號清單',\n    subscript: '下標',\n    superscript: '上標',\n    hyperlink: '超連結',\n    toggleFullscreen: '切換全螢幕',\n    quote: '段落引用',\n    left: '靠左對齊',\n    center: '置中對齊',\n    right: '靠右對齊',\n    justify: '分散對齊',\n    print: '列印',\n    outdent: '減少縮排',\n    indent: '增加縮排',\n    removeFormat: '清除格式',\n    formatting: '區塊元素',\n    fontSize: '字型大小',\n    align: '對齊',\n    hr: '水平分隔線',\n    undo: '復原',\n    redo: '取消復原',\n    heading1: '標題 1',\n    heading2: '標題 2',\n    heading3: '標題 3',\n    heading4: '標題 4',\n    heading5: '標題 5',\n    heading6: '標題 6',\n    paragraph: '段落',\n    code: '程式碼',\n    size1: '非常小',\n    size2: '稍小',\n    size3: '正常',\n    size4: '稍大',\n    size5: '大',\n    size6: '非常大',\n    size7: '超級大',\n    defaultFont: '預設字型',\n    viewSource: '切換原始碼'\n  },\n  tree: {\n    noNodes: '沒有節點',\n    noResults: '沒有相符節點'\n  }\n}\n", "import d from \"./node_modules/quasar/lang/zh-tw.js\";export default d;"], "mappings": ";;;AAAA,IAAO,gBAAQ;AAAA,EACb,SAAS;AAAA,EACT,YAAY;AAAA,EACZ,OAAO;AAAA,IACL,OAAO;AAAA,IACP,IAAI;AAAA,IACJ,QAAQ;AAAA,IACR,OAAO;AAAA,IACP,KAAK;AAAA,IACL,QAAQ;AAAA,IACR,OAAO;AAAA,IACP,QAAQ;AAAA,IACR,QAAQ;AAAA,IACR,QAAQ;AAAA,IACR,QAAQ;AAAA,IACR,QAAQ;AAAA,IACR,SAAS;AAAA,IACT,QAAQ,WAAU,QAAQ,gBAAO,WAAY;AAAA,IAC7C,UAAU,WAAU,QAAQ,gBAAO,WAAY;AAAA,EACjD;AAAA,EACA,MAAM;AAAA,IACJ,MAAM,uIAA8B,MAAM,GAAG;AAAA,IAC7C,WAAW,6FAAuB,MAAM,GAAG;AAAA,IAC3C,QAAQ,0KAAwC,MAAM,GAAG;AAAA,IACzD,aAAa,0KAAwC,MAAM,GAAG;AAAA,IAC9D,aAAa,UAAQ,IAAI,KAAK,eAAe,SAAS;AAAA,MACpD,SAAS;AAAA,MAAS,OAAO;AAAA,MAAS,KAAK;AAAA,IACzC,CAAC,EAAE,OAAO,IAAI;AAAA,IACd,gBAAgB;AAAA,IAChB,WAAW;AAAA,IACX,WAAW;AAAA,EACb;AAAA,EACA,OAAO;AAAA,IACL,QAAQ;AAAA,IACR,WAAW;AAAA,IACX,SAAS;AAAA,IACT,iBAAiB,UAAQ,wBAAS,OAAO;AAAA,IACzC,gBAAgB;AAAA,IAChB,SAAS;AAAA,IACT,YAAY,CAAC,OAAO,KAAK,UAAU,QAAQ,MAAM,MAAM,yBAAU,QAAQ;AAAA,IACzE,SAAS;AAAA,EACX;AAAA,EACA,QAAQ;AAAA,IACN,KAAK;AAAA,IACL,MAAM;AAAA,IACN,QAAQ;AAAA,IACR,eAAe;AAAA,IACf,WAAW;AAAA,IACX,eAAe;AAAA,IACf,aAAa;AAAA,IACb,WAAW;AAAA,IACX,aAAa;AAAA,IACb,WAAW;AAAA,IACX,kBAAkB;AAAA,IAClB,OAAO;AAAA,IACP,MAAM;AAAA,IACN,QAAQ;AAAA,IACR,OAAO;AAAA,IACP,SAAS;AAAA,IACT,OAAO;AAAA,IACP,SAAS;AAAA,IACT,QAAQ;AAAA,IACR,cAAc;AAAA,IACd,YAAY;AAAA,IACZ,UAAU;AAAA,IACV,OAAO;AAAA,IACP,IAAI;AAAA,IACJ,MAAM;AAAA,IACN,MAAM;AAAA,IACN,UAAU;AAAA,IACV,UAAU;AAAA,IACV,UAAU;AAAA,IACV,UAAU;AAAA,IACV,UAAU;AAAA,IACV,UAAU;AAAA,IACV,WAAW;AAAA,IACX,MAAM;AAAA,IACN,OAAO;AAAA,IACP,OAAO;AAAA,IACP,OAAO;AAAA,IACP,OAAO;AAAA,IACP,OAAO;AAAA,IACP,OAAO;AAAA,IACP,OAAO;AAAA,IACP,aAAa;AAAA,IACb,YAAY;AAAA,EACd;AAAA,EACA,MAAM;AAAA,IACJ,SAAS;AAAA,IACT,WAAW;AAAA,EACb;AACF;;;AC3FoD,IAAO,+BAAQ;", "names": []}