import { defineStore } from 'pinia';
import { useRouter } from 'vue-router';
import { Dialog } from 'quasar';
import { LoginResponse, TokenResponse, User } from '@/api/modules/auth';

// WebSocket連接
let ws: WebSocket | null = null;

export const useAuthStore = defineStore('auth', {
  state: () => ({
    user: null as User | null,
    accessToken: '',
    tokenExpiry: null as Date | null,
    refreshToken: '',
    refreshTokenExpiry: null as Date | null,
    // 添加一個標記來避免循環更新
    _isUpdatingFromStorage: false,
  }),
  persist: {
    // 使用 pinia-plugin-persistedstate 的配置
    key: 'auth-store',
    storage: localStorage,
    // 只持久化必要的字段，避免與手動 localStorage 操作衝突
    pick: ['user'],
  },
  getters: {
    userInfo: (state) => state.user,
    isAuthenticated: (state) => !!state.accessToken,
    isTokenExpired: (state) => {
      if (!state.tokenExpiry) {
        return true;
      }

      return new Date() >= state.tokenExpiry;
    },
    isRefreshTokenExpired: (state) => {
      if (!state.refreshTokenExpiry) {
        return true;
      }

      return new Date() >= state.refreshTokenExpiry;
    },
  },
  actions: {
    // 初始化跨分頁同步監聽器
    initCrossTabSync() {
      // 監聽 storage 事件，實現跨分頁同步
      window.addEventListener('storage', (event) => {
        if (this._isUpdatingFromStorage) return;

        // 監聽 token 更新事件
        if (event.key === 'auth-token-sync' && event.newValue) {
          try {
            const tokenData = JSON.parse(event.newValue);
            this._isUpdatingFromStorage = true;
            this.accessToken = tokenData.access_token;
            this.tokenExpiry = new Date(tokenData.expires_at);
            this.refreshToken = tokenData.refresh_token;
            this.refreshTokenExpiry = new Date(tokenData.refresh_expires_at);
            this._isUpdatingFromStorage = false;
          } catch (error) {
            console.error('Failed to sync token from storage:', error);
          }
        }

        // 監聽登出事件
        if (event.key === 'auth-logout-sync' && event.newValue === 'true') {
          this._isUpdatingFromStorage = true;
          this.user = null;
          this.accessToken = '';
          this.tokenExpiry = null;
          this.refreshToken = '';
          this.refreshTokenExpiry = null;
          this._isUpdatingFromStorage = false;

          // 跳轉到登入頁面
          const router = useRouter();
          router.push('/login');
        }
      });
    },

    // 初始化應用時檢查登入狀態並建立WebSocket連接
    initializeApp() {
      // 從localStorage恢復token狀態
      const accessToken = localStorage.getItem('accessToken');
      const tokenExpiry = localStorage.getItem('tokenExpiry');
      const refreshToken = localStorage.getItem('refreshToken');
      const refreshTokenExpiry = localStorage.getItem('refreshTokenExpiry');

      if (accessToken && tokenExpiry && refreshToken && refreshTokenExpiry) {
        this.accessToken = accessToken;
        this.tokenExpiry = new Date(tokenExpiry);
        this.refreshToken = refreshToken;
        this.refreshTokenExpiry = new Date(refreshTokenExpiry);

        // 如果用戶已登入且token未過期，建立WebSocket連接
        if (this.isAuthenticated && !this.isTokenExpired) {
          console.log('檢測到有效的登入狀態，建立WebSocket連接');
          this.setupWebSocket();
        }
      }

      // 監聽頁面可見性變化，確保WebSocket連接
      document.addEventListener('visibilitychange', () => {
        if (!document.hidden && this.isAuthenticated && !this.isTokenExpired) {
          // 頁面變為可見且用戶已登入，檢查WebSocket連接
          if (!ws || ws.readyState !== WebSocket.OPEN) {
            console.log('頁面可見，重新建立WebSocket連接');
            this.setupWebSocket();
          }
        }
      });
    },

    login(data: LoginResponse) {
      this.user = data.user;
      this.updateToken(data);

      // 登入成功後建立WebSocket連接
      this.setupWebSocket();
    },
    getTokenClaims() {
      const accessToken = this.accessToken;
      if (!accessToken) {
        return null;
      }

      try {
        // 解碼 accessToken
        const base64Url = accessToken.split('.')[1];
        const base64 = base64Url.replace(/-/g, '+').replace(/_/g, '/');
        const claims = JSON.parse(atob(base64));

        return {
          user_id: claims.user_id,
          is_admin: claims.is_admin,
        };
      } catch {
        return null;
      }
    },
    isAdmin() {
      const claims = this.getTokenClaims();
      return claims?.is_admin || false;
    },
    updateToken(data: TokenResponse) {
      // 避免在同步過程中觸發循環更新
      if (this._isUpdatingFromStorage) return;

      this.accessToken = data.access_token;
      this.tokenExpiry = data.expires_at;
      this.refreshToken = data.refresh_token;
      this.refreshTokenExpiry = data.refresh_expires_at;

      // 更新 localStorage（保持向後兼容）
      localStorage.setItem('accessToken', data.access_token);
      localStorage.setItem('tokenExpiry', data.expires_at.toString());
      localStorage.setItem('refreshToken', data.refresh_token);
      localStorage.setItem(
        'refreshTokenExpiry',
        data.refresh_expires_at.toString()
      );

      // 觸發跨分頁同步事件
      this.broadcastTokenUpdate(data);
    },

    // 廣播 token 更新到其他分頁
    broadcastTokenUpdate(data: TokenResponse) {
      try {
        // 使用臨時的 localStorage 項目來觸發 storage 事件
        const syncData = {
          access_token: data.access_token,
          expires_at: data.expires_at,
          refresh_token: data.refresh_token,
          refresh_expires_at: data.refresh_expires_at,
          timestamp: Date.now()
        };

        localStorage.setItem('auth-token-sync', JSON.stringify(syncData));
        // 立即移除，避免污染 localStorage
        setTimeout(() => {
          localStorage.removeItem('auth-token-sync');
        }, 100);
      } catch (error) {
        console.error('Failed to broadcast token update:', error);
      }
    },
    logout() {
      // 避免在同步過程中觸發循環更新
      if (this._isUpdatingFromStorage) return;

      // 關閉WebSocket連接
      if (ws) {
        ws.close(1000, 'User logout'); // 正常關閉
        ws = null;
      }

      this.accessToken = '';
      this.tokenExpiry = null;
      this.refreshToken = '';
      this.refreshTokenExpiry = null;
      this.user = null;

      localStorage.removeItem('accessToken');
      localStorage.removeItem('tokenExpiry');
      localStorage.removeItem('refreshToken');
      localStorage.removeItem('refreshTokenExpiry');

      // 觸發跨分頁登出同步事件
      this.broadcastLogout();
    },

    // 廣播登出事件到其他分頁
    broadcastLogout() {
      try {
        localStorage.setItem('auth-logout-sync', 'true');
        // 立即移除，避免污染 localStorage
        setTimeout(() => {
          localStorage.removeItem('auth-logout-sync');
        }, 100);
      } catch (error) {
        console.error('Failed to broadcast logout:', error);
      }
    },
    // 從 localStorage 初始化 token 數據（用於向後兼容）
    initializeFromStorage() {
      const accessToken = localStorage.getItem('accessToken');
      const refreshToken = localStorage.getItem('refreshToken');
      const tokenExpiry = localStorage.getItem('tokenExpiry');
      const refreshTokenExpiry = localStorage.getItem('refreshTokenExpiry');

      if (accessToken && refreshToken && tokenExpiry && refreshTokenExpiry) {
        this.accessToken = accessToken;
        this.tokenExpiry = new Date(tokenExpiry);
        this.refreshToken = refreshToken;
        this.refreshTokenExpiry = new Date(refreshTokenExpiry);
      }
    },
    setupWebSocket() {
      // 關閉現有連接
      if (ws) {
        ws.close();
        ws = null;
      }

      // 檢查是否有有效的token
      const token = this.accessToken || localStorage.getItem('accessToken');
      if (!token) {
        console.log('沒有有效的token，跳過WebSocket連接');
        return;
      }

      // 建立WebSocket連接
      let wsUrl = import.meta.env.VITE_WS_URL;

      // 如果沒有設置環境變數，根據當前環境自動判斷
      if (!wsUrl) {
        const protocol = window.location.protocol === 'https:' ? 'wss:' : 'ws:';
        const host = window.location.hostname;
        // WebSocket直接連接到後端服務器
        const port = '8088';
        wsUrl = `${protocol}//${host}:${port}/api/ws`;
      }

      console.log('嘗試建立WebSocket連接:', wsUrl);

      try {
        ws = new WebSocket(`${wsUrl}?token=${token}`);

        ws.onopen = () => {
          console.log('✅ WebSocket連接已建立');
        };

        ws.onmessage = (event: MessageEvent) => {
          try {
            console.log('📨 收到WebSocket消息:', event.data);
            const data = JSON.parse(event.data);

            if (data.type === 'forced_logout') {
              console.log('🚨 收到強制登出通知:', data.message);

              // 使用Dialog顯示強制登出消息
              Dialog.create({
                title: '帳號安全提醒',
                message: data.message || '您的帳號已在另一個設備登入，將自動登出',
                persistent: true,
                ok: {
                  label: '確定',
                  color: 'primary'
                }
              }).onOk(() => {
                // 執行登出操作
                this.logout();

                // 跳轉到登入頁面
                window.location.href = '/login';
              });
            }
          } catch (error) {
            console.error('處理WebSocket消息時發生錯誤:', error);
          }
        };

        ws.onclose = (event) => {
          console.log('❌ WebSocket連接已關閉:', event.code, event.reason);
          ws = null;

          // 如果不是正常關閉且用戶仍然登入，嘗試重新連接
          if (event.code !== 1000 && this.isAuthenticated && !this.isTokenExpired) {
            console.log('🔄 5秒後嘗試重新連接WebSocket...');
            setTimeout(() => {
              if (this.isAuthenticated && !this.isTokenExpired) {
                this.setupWebSocket();
              }
            }, 5000);
          }
        };

        ws.onerror = (error) => {
          console.error('❌ WebSocket連接錯誤:', error);
        };
      } catch (error) {
        console.error('❌ 建立WebSocket連接失敗:', error);
      }
    },
  },
});

export type AuthStore = ReturnType<typeof useAuthStore>;
