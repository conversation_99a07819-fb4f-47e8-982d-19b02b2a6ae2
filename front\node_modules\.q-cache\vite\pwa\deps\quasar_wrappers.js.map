{"version": 3, "sources": ["../../../../quasar/wrappers/index.js"], "sourcesContent": ["// Functions in this file are no-op,\n//  they just take a callback function and return it\n// They're used to apply typings to the callback\n//  parameters and return value when using Quasar with TypeScript\n// We need these in `ui` folder to make `quasar/wrapper` import work,\n//  but they are useful only for Quasar CLI projects\n// They are typed via module augmentation by `@quasar/app-webpack` / `@quasar/app-vite`\n\nexport function boot (callback) {\n  return callback\n}\n\nexport function configure (callback) {\n  return callback\n}\n\nexport function preFetch (callback) {\n  return callback\n}\n\nexport function route (callback) {\n  return callback\n}\n\nexport function store (callback) {\n  return callback\n}\n\nexport function ssrMiddleware (callback) {\n  return callback\n}\n\nexport function bexBackground (callback) {\n  return callback\n}\n\nexport function bexContent (callback) {\n  return callback\n}\n\nexport function bexDom (callback) {\n  return callback\n}\n\n/**\n * Below only for @quasar/app-webpack v3\n */\n\nexport function ssrProductionExport (callback) {\n  return callback\n}\n\n/**\n * Below only for @quasar/app-vite & @quasar/app-webpack v4+\n */\n\nexport function ssrCreate (callback) {\n  return callback\n}\n\nexport function ssrListen (callback) {\n  return callback\n}\n\nexport function ssrClose (callback) {\n  return callback\n}\n\nexport function ssrServeStaticContent (callback) {\n  return callback\n}\n\nexport function ssrRenderPreloadTag (callback) {\n  return callback\n}\n"], "mappings": ";;;AAQO,SAAS,KAAM,UAAU;AAC9B,SAAO;AACT;AAEO,SAAS,UAAW,UAAU;AACnC,SAAO;AACT;AAEO,SAAS,SAAU,UAAU;AAClC,SAAO;AACT;AAEO,SAAS,MAAO,UAAU;AAC/B,SAAO;AACT;AAEO,SAAS,MAAO,UAAU;AAC/B,SAAO;AACT;AAEO,SAAS,cAAe,UAAU;AACvC,SAAO;AACT;AAEO,SAAS,cAAe,UAAU;AACvC,SAAO;AACT;AAEO,SAAS,WAAY,UAAU;AACpC,SAAO;AACT;AAEO,SAAS,OAAQ,UAAU;AAChC,SAAO;AACT;AAMO,SAAS,oBAAqB,UAAU;AAC7C,SAAO;AACT;AAMO,SAAS,UAAW,UAAU;AACnC,SAAO;AACT;AAEO,SAAS,UAAW,UAAU;AACnC,SAAO;AACT;AAEO,SAAS,SAAU,UAAU;AAClC,SAAO;AACT;AAEO,SAAS,sBAAuB,UAAU;AAC/C,SAAO;AACT;AAEO,SAAS,oBAAqB,UAAU;AAC7C,SAAO;AACT;", "names": []}