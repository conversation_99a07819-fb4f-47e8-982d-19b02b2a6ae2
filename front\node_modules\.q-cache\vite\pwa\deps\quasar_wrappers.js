import "./chunk-JC4IRQUL.js";

// node_modules/quasar/wrappers/index.js
function boot(callback) {
  return callback;
}
function configure(callback) {
  return callback;
}
function preFetch(callback) {
  return callback;
}
function route(callback) {
  return callback;
}
function store(callback) {
  return callback;
}
function ssrMiddleware(callback) {
  return callback;
}
function bexBackground(callback) {
  return callback;
}
function bexContent(callback) {
  return callback;
}
function bexDom(callback) {
  return callback;
}
function ssrProductionExport(callback) {
  return callback;
}
function ssrCreate(callback) {
  return callback;
}
function ssrListen(callback) {
  return callback;
}
function ssrClose(callback) {
  return callback;
}
function ssrServeStaticContent(callback) {
  return callback;
}
function ssrRenderPreloadTag(callback) {
  return callback;
}
export {
  bexBackground,
  bexContent,
  bexDom,
  boot,
  configure,
  preFetch,
  route,
  ssrClose,
  ssrCreate,
  ssrListen,
  ssrMiddleware,
  ssrProductionExport,
  ssrRenderPreloadTag,
  ssrServeStaticContent,
  store
};
//# sourceMappingURL=quasar_wrappers.js.map
