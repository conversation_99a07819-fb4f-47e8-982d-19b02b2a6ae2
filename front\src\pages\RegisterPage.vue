<template>
  <q-page>
    <!-- 步驟一：閱讀服務條款與同意書 -->
    <div v-if="currentStep === 1">
      <q-card class="q-mx-auto q-py-lg q-px-md" style="max-width: min(100%, 50rem)">
        <q-card-section>
          <div class="text-h5 text-center text-primary q-mb-md">
            <q-icon name="assignment" class="q-mr-sm" />
            使用者服務條款與同意書
          </div>

          <q-separator class="q-mb-md" />

          <!-- 服務條款內容 -->
          <div class="agreement-content" style="max-height: 60vh; overflow-y: auto;">
            <div class="text-h6 q-mb-md text-primary">
              <q-icon name="info" class="q-mr-sm" />
              歡迎使用樂透分析系統
            </div>
            <p class="text-body1 q-mb-md">
              感謝您選擇使用我們的樂透分析系統。在開始使用本系統前，請仔細閱讀以下服務條款與同意書。使用本系統即表示您已完全了解並同意遵守以下所有條款。
            </p>

            <div class="text-h6 q-mb-sm text-orange">第一條 服務說明</div>
            <div class="q-mb-md">
              <div class="text-subtitle1 text-weight-bold q-mb-xs">1.1 服務內容</div>
              <p class="text-body2 q-mb-sm">本系統提供樂透相關資訊服務，包括但不限於：</p>
              <ul class="q-pl-md q-mb-sm">
                <li>開獎號碼查詢與統計</li>
                <li>歷史數據分析</li>
                <li>趨勢圖表展示</li>
                <li>數據統計功能</li>
              </ul>
              <div class="text-subtitle1 text-weight-bold q-mb-xs">1.2 服務性質</div>
              <p class="text-body2 q-mb-sm">
                本系統僅提供資訊查詢與分析功能，<span class="text-weight-bold text-red">不參與任何形式的彩券販售、投注或金錢交易</span>。
              </p>

              <div class="text-subtitle1 text-weight-bold q-mb-xs">1.3 收費說明</div>
              <q-banner class="bg-blue-1 text-blue-8 q-mb-md" rounded>
                <template v-slot:avatar>
                  <q-icon name="payment" color="blue" />
                </template>
                <div class="text-subtitle2 text-weight-bold">
                  本系統採用<span class="text-weight-bold text-blue">月費制收費模式</span>，使用者需要支付月費才能使用完整的系統功能。
                  <br>請確認您了解並同意相關收費條款後再進行註冊。
                </div>
              </q-banner>
            </div>

            <div class="text-h6 q-mb-sm text-orange">第二條 使用規範</div>
            <div class="q-mb-md">
              <div class="text-subtitle1 text-weight-bold q-mb-xs">2.1 帳號管理</div>
              <ul class="q-pl-md q-mb-sm">
                <li>使用者應妥善保管帳號密碼，不得與他人共享</li>
                <li>如發現帳號被盜用，應立即通知系統管理員</li>
                <li>一人限註冊一個帳號，禁止重複註冊</li>
              </ul>

              <div class="text-subtitle1 text-weight-bold q-mb-xs">2.2 使用行為規範</div>
              <p class="text-body2 q-mb-xs">使用者在使用本系統時，<span class="text-weight-bold text-red">嚴禁</span>進行以下行為：</p>
              <ul class="q-pl-md q-mb-sm">
                <li>利用系統漏洞進行惡意攻擊或破壞</li>
                <li>散布病毒、惡意程式或有害內容</li>
                <li>進行任何違法或不當的商業活動</li>
                <li>干擾系統正常運作或影響其他使用者權益</li>
                <li>嘗試破解、逆向工程或竊取系統資料</li>
              </ul>
            </div>

            <div class="text-h6 q-mb-sm text-orange">第三條 免責聲明</div>
            <q-banner class="bg-orange-1 text-orange-8 q-mb-md" rounded>
              <template v-slot:avatar>
                <q-icon name="warning" color="orange" />
              </template>
              <div class="text-subtitle1 text-weight-bold">
                本系統提供之樂透相關資訊，包括開獎號碼、統計分析、數據趨勢等，僅供
                <span class="text-weight-bold text-primary">娛樂與參考用途</span>。
                所有資訊皆源自公開資料，我們致力於維持資料的正確性與即時性，但無法保證內容始終完整、正確或最新。
              </div>
            </q-banner>

            <q-banner class="bg-red-1 text-red-8 q-mb-md" rounded>
              <template v-slot:avatar>
                <q-icon name="error" color="red" />
              </template>
              <div class="text-subtitle1 text-weight-bold">
                若本系統所呈現之資料與官方公告有所差異，請一律以
                <span class="text-red text-weight-bold">台灣彩券公司公告之資訊為準</span>。
                使用者應自行判斷並承擔使用本系統資訊所可能帶來的風險與後果。
              </div>
            </q-banner>

            <div class="text-h6 q-mb-sm text-orange">第四條 收費與退費政策</div>
            <div class="q-mb-md">
              <p class="text-body2 q-mb-sm">本系統採用月費制，使用者需按月支付服務費用。如因系統問題導致服務中斷，將按比例退還費用。</p>
            </div>

            <div class="text-h6 q-mb-sm text-orange">第五條 隱私保護</div>
            <div class="q-mb-md">
              <p class="text-body2 q-mb-sm">我們僅收集註冊時必要的基本資料，並承諾妥善保護使用者個人資料，不會將資料提供給第三方。</p>
            </div>

            <div class="text-center q-mt-lg">
              <q-icon name="check_circle" class="q-mr-sm text-positive" />
              <span class="text-subtitle1 text-grey-7">
                使用本系統即表示您已了解並同意本服務條款之內容。
              </span>
            </div>
          </div>
        </q-card-section>

        <q-separator />

        <q-card-section>
          <div class="text-h6 q-mb-md text-center">同意聲明</div>

          <div class="q-pa-md bg-grey-1 rounded-borders">
            <q-checkbox
              v-model="agreements.agreeAll"
              color="primary"
              class="text-body1"
            >
              <div class="q-ml-sm">
                <span class="text-weight-bold">我已詳細閱讀並完全了解上述所有服務條款內容</span>，包括收費政策、使用規範、免責聲明等，並同意遵守所有條款規定。我了解本系統採用<span class="text-weight-bold text-blue">月費制收費</span>，且僅供娛樂參考用途，不保證任何投注結果。
              </div>
            </q-checkbox>
          </div>
        </q-card-section>

        <q-card-actions class="q-px-md q-pb-md">
          <q-btn
            flat
            to="/login"
            label="返回登入"
            class="q-mr-sm"
            color="grey"
          />
          <q-spacer />
          <q-btn
            :disabled="!agreements.agreeAll"
            @click="goToRegisterForm"
            label="同意並繼續註冊"
            color="primary"
            :ripple="{ center: true }"
          />
        </q-card-actions>
      </q-card>
    </div>

    <!-- 步驟二：填寫註冊資料 -->
    <div v-if="currentStep === 2">
      <q-form ref="formRef" @submit="onRegister" class="q-py-lg">
        <q-card
          class="q-mx-auto q-py-lg q-px-md"
          style="max-width: min(100%, 28rem)"
        >
          <q-card-section class="q-gutter-md">
            <div class="text-h6 text-center">
              <q-icon name="person_add" class="q-mr-sm" />
              填寫註冊資料
            </div>

            <q-stepper-navigation class="q-mb-md">
              <q-btn
                flat
                @click="backToAgreement"
                label="返回條款"
                icon="arrow_back"
                color="grey"
                size="sm"
              />
            </q-stepper-navigation>

            <q-input
              type="text"
              v-model="uid"
              label="手機號碼(帳號)"
              stack-label
              placeholder="09xxxxxxxx"
              mask="##########"
              unmasked-value
              outlined
              lazy-rules
              :rules="[
                (val) => !!val || '請輸入手機號碼',
                (val) => validatePhone(val) || '手機號碼格式錯誤',
              ]"
            />
            <q-input
              type="password"
              v-model="password"
              label="密碼"
              outlined
              lazy-rules
              :rules="[
                (val) => !!val || '請輸入密碼',
                (val) => val.length >= 4 || '密碼長度需大於 4',
              ]"
            />
            <q-input
              type="password"
              v-model="confirmPassword"
              label="確認密碼"
              outlined
              lazy-rules
              :rules="[(val) => val === password || '密碼不一致']"
            />

            <!-- 基本資料 -->
            <q-input
              type="text"
              v-model="name"
              label="姓名"
              outlined
              :rules="[(val) => !!val || '請輸入姓名']"
            />
          </q-card-section>

          <q-card-actions>
            <q-btn
              rounded
              type="submit"
              :loading="isRegistering"
              color="login"
              label="完成註冊"
              class="full-width q-mt-md"
              size="lg"
              :ripple="{ center: true }"
            />
          </q-card-actions>

          <q-card-actions>
            <q-btn
              dense
              flat
              to="/login"
              label="已有帳號，返回登入"
              class="full-width q-mt-md"
              size="md"
              text-color="primary"
              :ripple="{ center: true }"
              style="font-weight: bold"
            />
          </q-card-actions>
        </q-card>
      </q-form>
    </div>
  </q-page>
</template>

<script setup lang="ts">
import { ref } from 'vue';
import { useRouter } from 'vue-router';
import { AUTH_API } from '@/api';
import { useDialog, validatePhone, handleError } from '@/utils';

const router = useRouter();

const formRef = ref<HTMLFormElement | null>(null);

// 註冊流程步驟
const currentStep = ref(1);

// 同意書勾選狀態
const agreements = ref({
  agreeAll: false,
});

const uid = ref('');
const password = ref('');
const confirmPassword = ref('');
const name = ref('');
const phone = ref('');
const email = ref('');
const isRegistering = ref(false);

// 步驟切換方法
const goToRegisterForm = () => {
  currentStep.value = 2;
};

const backToAgreement = () => {
  currentStep.value = 1;
};

const resetForm = () => {
  uid.value = '';
  password.value = '';
  confirmPassword.value = '';
  name.value = '';
  phone.value = '';
  email.value = '';

  formRef.value?.reset();
};

const onRegister = async () => {
  isRegistering.value = true;

  try {
    await AUTH_API.register({
      uid: uid.value,
      pwd: password.value,
      name: name.value,
      phone: phone.value,
      email: email.value,
    });

    resetForm();

    const dialog = useDialog();
    dialog.showMessage({
      title: '註冊成功',
      message: '即將前往登入頁面',
      timeout: 1500,
      color: 'positive',
      onRedirect: () => {
        router.push('/login');
      },
    });
  } catch (error) {
    handleError(error);
  } finally {
    isRegistering.value = false;
  }
};
</script>

<style scoped>
.agreement-content {
  line-height: 1.6;
}

.agreement-content ul {
  margin: 0;
  padding-left: 1.2rem;
}

.agreement-content li {
  margin-bottom: 0.25rem;
}

.agreement-content p {
  margin-bottom: 0.5rem;
}

/* 滾動條樣式 */
.agreement-content::-webkit-scrollbar {
  width: 6px;
}

.agreement-content::-webkit-scrollbar-track {
  background: #f1f1f1;
  border-radius: 3px;
}

.agreement-content::-webkit-scrollbar-thumb {
  background: #c1c1c1;
  border-radius: 3px;
}

.agreement-content::-webkit-scrollbar-thumb:hover {
  background: #a8a8a8;
}

/* 響應式設計 */
@media (max-width: 600px) {
  .agreement-content {
    max-height: 50vh;
  }
}
</style>
