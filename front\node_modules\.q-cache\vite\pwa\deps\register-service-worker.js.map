{"version": 3, "sources": ["../../../../register-service-worker/index.js"], "sourcesContent": ["// Register a service worker to serve assets from local cache.\n\n// This lets the app load faster on subsequent visits in production, and gives\n// it offline capabilities. However, it also means that developers (and users)\n// will only see deployed updates on the \"N+1\" visit to a page, since previously\n// cached resources are updated in the background.\n\nvar isLocalhost = function () { return Boolean(\n  window.location.hostname === 'localhost' ||\n    // [::1] is the IPv6 localhost address.\n    window.location.hostname === '[::1]' ||\n    // 127.0.0.1/8 is considered localhost for IPv4.\n    window.location.hostname.match(\n      /^127(?:\\.(?:25[0-5]|2[0-4][0-9]|[01]?[0-9][0-9]?)){3}$/\n    )\n); }\n\nvar waitWindowLoad\n// https://github.com/yyx990803/register-service-worker/pull/33#discussion_r394181861\nif (typeof window !== 'undefined') {\n  // Typically, a browser that supports `serviceWorker` should also have supported\n  // `Promise`. But as this package can be used in environments without service\n  // worker support (in that case it would do nothing), there's a chance that\n  // `Promise` does not exist. So we must check for its existence first.\n  if (typeof Promise !== 'undefined') {\n    waitWindowLoad = new Promise(function (resolve) { return window.addEventListener('load', resolve); })\n  } else {\n    waitWindowLoad = { then: function (cb) { return window.addEventListener('load', cb); } }\n  }\n}\n\nexport function register (swUrl, hooks) {\n  if ( hooks === void 0 ) hooks = {};\n\n  var registrationOptions = hooks.registrationOptions; if ( registrationOptions === void 0 ) registrationOptions = {};\n  delete hooks.registrationOptions\n\n  var emit = function (hook) {\n    var args = [], len = arguments.length - 1;\n    while ( len-- > 0 ) args[ len ] = arguments[ len + 1 ];\n\n    if (hooks && hooks[hook]) {\n      hooks[hook].apply(hooks, args)\n    }\n  }\n\n  if ('serviceWorker' in navigator) {\n    waitWindowLoad.then(function () {\n      if (isLocalhost()) {\n        // This is running on localhost. Lets check if a service worker still exists or not.\n        checkValidServiceWorker(swUrl, emit, registrationOptions)\n        navigator.serviceWorker.ready.then(function (registration) {\n          emit('ready', registration)\n        }).catch(function (error) { return handleError(emit, error); })\n      } else {\n        // Is not local host. Just register service worker\n        registerValidSW(swUrl, emit, registrationOptions)\n        navigator.serviceWorker.ready.then(function (registration) {\n          emit('ready', registration)\n        }).catch(function (error) { return handleError(emit, error); })\n      }\n    })\n  }\n}\n\nfunction handleError (emit, error) {\n  if (!navigator.onLine) {\n    emit('offline')\n  }\n  emit('error', error)\n}\n\nfunction registerValidSW (swUrl, emit, registrationOptions) {\n  navigator.serviceWorker\n    .register(swUrl, registrationOptions)\n    .then(function (registration) {\n      emit('registered', registration)\n      if (registration.waiting) {\n        emit('updated', registration)\n        return\n      }\n      registration.onupdatefound = function () {\n        emit('updatefound', registration)\n        var installingWorker = registration.installing\n        installingWorker.onstatechange = function () {\n          if (installingWorker.state === 'installed') {\n            if (navigator.serviceWorker.controller) {\n              // At this point, the old content will have been purged and\n              // the fresh content will have been added to the cache.\n              // It's the perfect time to display a \"New content is\n              // available; please refresh.\" message in your web app.\n              emit('updated', registration)\n            } else {\n              // At this point, everything has been precached.\n              // It's the perfect time to display a\n              // \"Content is cached for offline use.\" message.\n              emit('cached', registration)\n            }\n          }\n        }\n      }\n    })\n    .catch(function (error) { return handleError(emit, error); })\n}\n\nfunction checkValidServiceWorker (swUrl, emit, registrationOptions) {\n  // Check if the service worker can be found.\n  fetch(swUrl)\n    .then(function (response) {\n      // Ensure service worker exists, and that we really are getting a JS file.\n      if (response.status === 404) {\n        // No service worker found.\n        emit('error', new Error((\"Service worker not found at \" + swUrl)))\n        unregister()\n      } else if (response.headers.get('content-type').indexOf('javascript') === -1) {\n        emit('error', new Error(\n          \"Expected \" + swUrl + \" to have javascript content-type, \" +\n          \"but received \" + (response.headers.get('content-type'))))\n        unregister()\n      } else {\n        // Service worker found. Proceed as normal.\n        registerValidSW(swUrl, emit, registrationOptions)\n      }\n    })\n    .catch(function (error) { return handleError(emit, error); })\n}\n\nexport function unregister () {\n  if ('serviceWorker' in navigator) {\n    navigator.serviceWorker.ready.then(function (registration) {\n      registration.unregister()\n    }).catch(function (error) { return handleError(emit, error); })\n  }\n}\n"], "mappings": ";;;AAOA,IAAI,cAAc,WAAY;AAAE,SAAO;AAAA,IACrC,OAAO,SAAS,aAAa,eAE3B,OAAO,SAAS,aAAa,WAE7B,OAAO,SAAS,SAAS;AAAA,MACvB;AAAA,IACF;AAAA,EACJ;AAAG;AAEH,IAAI;AAEJ,IAAI,OAAO,WAAW,aAAa;AAKjC,MAAI,OAAO,YAAY,aAAa;AAClC,qBAAiB,IAAI,QAAQ,SAAU,SAAS;AAAE,aAAO,OAAO,iBAAiB,QAAQ,OAAO;AAAA,IAAG,CAAC;AAAA,EACtG,OAAO;AACL,qBAAiB,EAAE,MAAM,SAAU,IAAI;AAAE,aAAO,OAAO,iBAAiB,QAAQ,EAAE;AAAA,IAAG,EAAE;AAAA,EACzF;AACF;AAEO,SAAS,SAAU,OAAO,OAAO;AACtC,MAAK,UAAU;AAAS,YAAQ,CAAC;AAEjC,MAAI,sBAAsB,MAAM;AAAqB,MAAK,wBAAwB;AAAS,0BAAsB,CAAC;AAClH,SAAO,MAAM;AAEb,MAAIA,QAAO,SAAU,MAAM;AACzB,QAAI,OAAO,CAAC,GAAG,MAAM,UAAU,SAAS;AACxC,WAAQ,QAAQ;AAAI,WAAM,OAAQ,UAAW,MAAM;AAEnD,QAAI,SAAS,MAAM,OAAO;AACxB,YAAM,MAAM,MAAM,OAAO,IAAI;AAAA,IAC/B;AAAA,EACF;AAEA,MAAI,mBAAmB,WAAW;AAChC,mBAAe,KAAK,WAAY;AAC9B,UAAI,YAAY,GAAG;AAEjB,gCAAwB,OAAOA,OAAM,mBAAmB;AACxD,kBAAU,cAAc,MAAM,KAAK,SAAU,cAAc;AACzD,UAAAA,MAAK,SAAS,YAAY;AAAA,QAC5B,CAAC,EAAE,MAAM,SAAU,OAAO;AAAE,iBAAO,YAAYA,OAAM,KAAK;AAAA,QAAG,CAAC;AAAA,MAChE,OAAO;AAEL,wBAAgB,OAAOA,OAAM,mBAAmB;AAChD,kBAAU,cAAc,MAAM,KAAK,SAAU,cAAc;AACzD,UAAAA,MAAK,SAAS,YAAY;AAAA,QAC5B,CAAC,EAAE,MAAM,SAAU,OAAO;AAAE,iBAAO,YAAYA,OAAM,KAAK;AAAA,QAAG,CAAC;AAAA,MAChE;AAAA,IACF,CAAC;AAAA,EACH;AACF;AAEA,SAAS,YAAaA,OAAM,OAAO;AACjC,MAAI,CAAC,UAAU,QAAQ;AACrB,IAAAA,MAAK,SAAS;AAAA,EAChB;AACA,EAAAA,MAAK,SAAS,KAAK;AACrB;AAEA,SAAS,gBAAiB,OAAOA,OAAM,qBAAqB;AAC1D,YAAU,cACP,SAAS,OAAO,mBAAmB,EACnC,KAAK,SAAU,cAAc;AAC5B,IAAAA,MAAK,cAAc,YAAY;AAC/B,QAAI,aAAa,SAAS;AACxB,MAAAA,MAAK,WAAW,YAAY;AAC5B;AAAA,IACF;AACA,iBAAa,gBAAgB,WAAY;AACvC,MAAAA,MAAK,eAAe,YAAY;AAChC,UAAI,mBAAmB,aAAa;AACpC,uBAAiB,gBAAgB,WAAY;AAC3C,YAAI,iBAAiB,UAAU,aAAa;AAC1C,cAAI,UAAU,cAAc,YAAY;AAKtC,YAAAA,MAAK,WAAW,YAAY;AAAA,UAC9B,OAAO;AAIL,YAAAA,MAAK,UAAU,YAAY;AAAA,UAC7B;AAAA,QACF;AAAA,MACF;AAAA,IACF;AAAA,EACF,CAAC,EACA,MAAM,SAAU,OAAO;AAAE,WAAO,YAAYA,OAAM,KAAK;AAAA,EAAG,CAAC;AAChE;AAEA,SAAS,wBAAyB,OAAOA,OAAM,qBAAqB;AAElE,QAAM,KAAK,EACR,KAAK,SAAU,UAAU;AAExB,QAAI,SAAS,WAAW,KAAK;AAE3B,MAAAA,MAAK,SAAS,IAAI,MAAO,iCAAiC,KAAM,CAAC;AACjE,iBAAW;AAAA,IACb,WAAW,SAAS,QAAQ,IAAI,cAAc,EAAE,QAAQ,YAAY,MAAM,IAAI;AAC5E,MAAAA,MAAK,SAAS,IAAI;AAAA,QAChB,cAAc,QAAQ,oDACH,SAAS,QAAQ,IAAI,cAAc;AAAA,MAAE,CAAC;AAC3D,iBAAW;AAAA,IACb,OAAO;AAEL,sBAAgB,OAAOA,OAAM,mBAAmB;AAAA,IAClD;AAAA,EACF,CAAC,EACA,MAAM,SAAU,OAAO;AAAE,WAAO,YAAYA,OAAM,KAAK;AAAA,EAAG,CAAC;AAChE;AAEO,SAAS,aAAc;AAC5B,MAAI,mBAAmB,WAAW;AAChC,cAAU,cAAc,MAAM,KAAK,SAAU,cAAc;AACzD,mBAAa,WAAW;AAAA,IAC1B,CAAC,EAAE,MAAM,SAAU,OAAO;AAAE,aAAO,YAAY,MAAM,KAAK;AAAA,IAAG,CAAC;AAAA,EAChE;AACF;", "names": ["emit"]}